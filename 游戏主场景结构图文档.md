# Goose Duck Kill 3 游戏主场景结构图文档

## 📋 概述

本文档详细描述了 **Goose Duck Kill 3** 游戏的主场景结构设计，包括场景层次结构、游戏对象组织、UI布局和网络同步架构。项目采用模块化场景设计，支持多种游戏状态和跨平台部署。

## 🎮 场景架构总览

### 场景分类
```
Assets/Scenes/
├── MainMenu/           # 主菜单场景
├── Lobby/             # 游戏大厅场景
├── Maps/              # 游戏地图场景集合
│   ├── Skeld/         # 经典地图
│   ├── Mira/          # 总部地图
│   └── Polus/         # 星球地图
└── SampleScene.unity  # 开发测试场景
```

### 游戏状态与场景映射
| 游戏状态 | 对应场景 | 主要功能 |
|---------|---------|----------|
| `MainMenu` | MainMenu | 启动界面、设置、连接服务器 |
| `Lobby` | Lobby | 房间管理、玩家准备、游戏配置 |
| `Starting` | Maps/* | 角色分配、倒计时、游戏准备 |
| `Playing` | Maps/* | 主要游戏循环、任务执行、互动 |
| `Meeting` | Maps/* | 会议讨论、投票系统 |
| `Ending` | Maps/* | 结果展示、统计信息 |

## 🏗️ 主场景结构设计

### 1. MainMenu 场景结构

```
MainMenu Scene
├── 🎨 UI Canvas (Screen Space - Overlay)
│   ├── 📱 Mobile UI Root (移动端适配)
│   │   ├── Safe Area
│   │   └── Mobile Controls
│   ├── 🖥️ Desktop UI Root
│   │   ├── Main Panel
│   │   │   ├── Title Logo
│   │   │   ├── Play Button
│   │   │   ├── Settings Button
│   │   │   └── Quit Button
│   │   ├── Settings Panel
│   │   │   ├── Audio Settings
│   │   │   ├── Graphics Settings
│   │   │   └── Controls Settings
│   │   └── Connection Panel
│   │       ├── Server Input
│   │       ├── Player Name Input
│   │       └── Connect Button
│   └── 🔧 UI Manager
│       ├── ConnectionUI Component
│       ├── SettingsManager Component
│       └── MobileUIAdapter Component
├── 🌐 Network Manager
│   ├── NetworkRunner Component
│   ├── NetworkManager Component
│   └── AuthManager Component
├── 🎵 Audio Manager
│   ├── Background Music
│   ├── UI Sound Effects
│   └── Audio Mixer Groups
├── 📱 Mobile Performance Optimizer
└── 🔍 Debug Console (Development Only)
```

### 2. Lobby 场景结构

```
Lobby Scene
├── 🎨 UI Canvas (Screen Space - Overlay)
│   ├── 📋 Room Info Panel
│   │   ├── Room Name Display
│   │   ├── Player Count (X/10)
│   │   ├── Game Settings Display
│   │   └── Room Code
│   ├── 👥 Player List Panel
│   │   ├── Player Entry Prefab (动态生成)
│   │   │   ├── Player Avatar
│   │   │   ├── Player Name
│   │   │   ├── Ready Status
│   │   │   └── Host Crown (if host)
│   │   └── Scroll View
│   ├── ⚙️ Game Settings Panel (Host Only)
│   │   ├── Map Selection
│   │   ├── Player Limits
│   │   ├── Game Rules
│   │   └── Apply Button
│   ├── 🎮 Control Panel
│   │   ├── Ready/Unready Button
│   │   ├── Start Game Button (Host Only)
│   │   ├── Leave Room Button
│   │   └── Chat Input
│   └── 📱 Mobile UI Adaptations
│       ├── Virtual Keyboard Support
│       └── Touch-Friendly Buttons
├── 🌐 Network Components
│   ├── RoomManager Component
│   ├── PlayerSpawner Component
│   └── LobbyNetworkManager Component
├── 🎵 Ambient Audio
└── 📊 Performance Monitor
```

### 3. Game Map 场景结构 (以Skeld为例)

```
Skeld Game Scene
├── 🗺️ Environment Root
│   ├── 🏢 Map Geometry
│   │   ├── Rooms
│   │   │   ├── Cafeteria
│   │   │   ├── Upper Engine
│   │   │   ├── Lower Engine
│   │   │   ├── Security
│   │   │   ├── Reactor
│   │   │   ├── Medbay
│   │   │   ├── Electrical
│   │   │   ├── Storage
│   │   │   ├── Admin
│   │   │   ├── Communications
│   │   │   ├── O2
│   │   │   ├── Weapons
│   │   │   ├── Shields
│   │   │   └── Navigation
│   │   ├── Corridors & Hallways
│   │   └── Vents System
│   ├── 🚪 Interactive Objects
│   │   ├── Doors (可关闭/破坏)
│   │   ├── Emergency Button
│   │   ├── Security Cameras
│   │   └── Admin Table
│   ├── 📋 Task Stations
│   │   ├── Task Prefab Instances
│   │   │   ├── Task Interaction Zone
│   │   │   ├── Task UI Trigger
│   │   │   ├── Visual Task Indicator
│   │   │   └── Network Sync Component
│   │   └── Task Manager
│   └── 💡 Lighting System
│       ├── Room Lights
│       ├── Emergency Lighting
│       └── Sabotage Effects
├── 👥 Players Root
│   ├── 🎮 Player Spawn Points
│   │   ├── Spawn Point (Cafeteria)
│   │   └── Respawn Points (各房间)
│   └── 👤 Player Prefab Instances (动态生成)
│       ├── PlayerController Component
│       ├── PlayerMovement Component
│       ├── PlayerInteraction Component
│       ├── PlayerNetwork Component
│       ├── PlayerAnimation Component
│       ├── Collider & Rigidbody
│       └── Visual Components
│           ├── Character Sprite
│           ├── Name Tag
│           ├── Color Customization
│           └── Role Indicator (隐藏)
├── 🎨 Game UI Canvas
│   ├── 📱 Mobile UI Root
│   │   ├── Virtual Joystick
│   │   ├── Action Buttons
│   │   │   ├── Use/Interact Button
│   │   │   ├── Kill Button (Duck Only)
│   │   │   ├── Report Button
│   │   │   ├── Emergency Button
│   │   │   └── Vent Button (Duck Only)
│   │   └── Mobile HUD
│   ├── 🖥️ Desktop UI Root
│   │   ├── Game HUD
│   │   │   ├── Task List
│   │   │   ├── Mini Map
│   │   │   ├── Emergency Cooldown
│   │   │   ├── Kill Cooldown (Duck Only)
│   │   │   └── Player Status
│   │   ├── Task UI Panel
│   │   │   ├── Task Instructions
│   │   │   ├── Task Progress
│   │   │   ├── Task Interaction
│   │   │   └── Task Complete Animation
│   │   ├── Meeting UI Panel
│   │   │   ├── Discussion Timer
│   │   │   ├── Voting Timer
│   │   │   ├── Player Vote Buttons
│   │   │   ├── Chat System
│   │   │   └── Vote Results
│   │   ├── Game Over Panel
│   │   │   ├── Victory/Defeat Screen
│   │   │   ├── Player Statistics
│   │   │   ├── Match Summary
│   │   │   └── Return to Lobby Button
│   │   └── Settings Overlay
│   │       ├── Audio Controls
│   │       ├── Graphics Settings
│   │       └── Controls Mapping
│   └── 🔧 UI Controllers
│       ├── GameUIManager Component
│       ├── TaskUIController Component
│       ├── MeetingUIController Component
│       └── MobileUIController Component
├── 🌐 Network & Game Management
│   ├── 🎮 Core Managers
│   │   ├── GameManager (Singleton)
│   │   │   ├── Game State Management
│   │   │   ├── Round Management
│   │   │   ├── Timer Management
│   │   │   └── Event Coordination
│   │   ├── GameStateManager
│   │   │   ├── State Transitions
│   │   │   └── State-Specific Logic
│   │   ├── RoleManager
│   │   │   ├── Role Assignment
│   │   │   ├── Role Abilities
│   │   │   └── Role Validation
│   │   └── ScoreManager
│   │       ├── Victory Conditions
│   │       ├── Score Tracking
│   │       └── Statistics
│   ├── 🌐 Network Components
│   │   ├── NetworkRunner
│   │   ├── NetworkManager
│   │   ├── RPC Manager
│   │   └── Authority Manager
│   ├── 📋 Task System
│   │   ├── TaskManager
│   │   ├── Task Definitions
│   │   ├── Task Progress Tracking
│   │   └── Task Completion Events
│   └── 🔧 Sabotage System
│       ├── SabotageManager
│       ├── Sabotage Types
│       │   ├── Lights
│       │   ├── Oxygen
│       │   ├── Reactor
│       │   └── Communications
│       └── Repair Mechanics
├── 🎵 Audio System
│   ├── 🎶 Background Music
│   │   ├── Ambient Tracks
│   │   ├── Tension Music
│   │   └── Meeting Music
│   ├── 🔊 Sound Effects
│   │   ├── Footsteps
│   │   ├── Task Sounds
│   │   ├── Kill Sounds
│   │   ├── Emergency Alarm
│   │   └── UI Sounds
│   └── 🎚️ Audio Management
│       ├── Audio Manager
│       ├── 3D Audio Sources
│       └── Audio Mixer Groups
├── 📱 Mobile Optimizations
│   ├── Performance Optimizer
│   ├── Battery Optimization
│   ├── Network Optimization
│   └── UI Scaling Adapter
├── 🔍 Debug & Development
│   ├── Debug Console
│   ├── Network Debug Info
│   ├── Performance Profiler
│   └── Cheat Commands (Development Only)
└── 📊 Analytics & Telemetry
    ├── Game Analytics
    ├── Performance Metrics
    └── Player Behavior Tracking
```

## 🔗 场景间通信与数据流

### 场景切换流程
```mermaid
graph TD
    A[MainMenu Scene] -->|Connect to Server| B[Lobby Scene]
    B -->|Start Game| C[Map Scene]
    C -->|Game End| D[Results Screen]
    D -->|Return to Lobby| B
    D -->|Disconnect| A

    C -->|Meeting Called| E[Meeting State]
    E -->|Meeting End| C

    C -->|Sabotage| F[Sabotage State]
    F -->|Repair Complete| C
```

### 数据持久化
- **PlayerPrefs**: 玩家设置、用户名、音频设置
- **Network State**: 游戏状态、玩家数据、房间信息
- **Session Data**: 当前游戏会话的临时数据

### 网络同步架构
```
Client A ←→ NetworkRunner ←→ Server ←→ NetworkRunner ←→ Client B
    ↓           ↓                ↓           ↓
GameManager GameManager    GameManager GameManager
    ↓           ↓                ↓           ↓
Local UI    Local UI        Local UI    Local UI
```

## 📱 移动端适配设计

### UI适配策略
- **Safe Area**: 自动适配刘海屏和圆角屏幕
- **Touch Controls**: 虚拟摇杆和触摸按钮
- **Responsive Layout**: 基于屏幕尺寸的动态布局
- **Performance Scaling**: 根据设备性能自动调整质量

### 移动端特殊组件
```
Mobile UI Root
├── Safe Area Handler
├── Virtual Joystick Controller
├── Touch Button Manager
├── Gesture Recognition System
├── Haptic Feedback Controller
└── Mobile Performance Monitor
```

## 🎯 最佳实践与设计原则

### 场景组织原则
1. **模块化设计**: 每个功能模块独立组织
2. **层次清晰**: 使用空GameObject作为组织容器
3. **命名规范**: 统一的命名约定便于维护
4. **预制体复用**: 最大化预制体的使用

### 性能优化策略
1. **对象池**: 频繁创建/销毁的对象使用对象池
2. **LOD系统**: 根据距离调整渲染质量
3. **批处理**: 合并相似的渲染调用
4. **异步加载**: 大型资源异步加载避免卡顿

### 网络优化
1. **状态同步**: 只同步必要的状态变化
2. **RPC优化**: 合理使用RPC的可靠性和优先级
3. **带宽管理**: 根据网络条件调整同步频率
4. **权限管理**: 明确的客户端/服务器权限划分

## 🛠️ 场景实现指南

### 1. 创建新场景的步骤

#### 基础场景设置
```csharp
// 1. 创建场景基础结构
public class SceneSetup : MonoBehaviour
{
    [MenuItem("Tools/Create Game Scene")]
    public static void CreateGameScene()
    {
        // 创建基础GameObject层次结构
        CreateSceneHierarchy();

        // 设置网络组件
        SetupNetworkComponents();

        // 配置UI Canvas
        SetupUICanvas();

        // 添加音频系统
        SetupAudioSystem();
    }

    private static void CreateSceneHierarchy()
    {
        // Environment Root
        var envRoot = new GameObject("Environment Root");

        // Players Root
        var playersRoot = new GameObject("Players Root");

        // Managers Root
        var managersRoot = new GameObject("Game Managers");

        // UI Root
        var uiRoot = new GameObject("UI Canvas");
    }
}
```

#### 网络组件配置
```csharp
// 2. 配置网络管理器
public class GameSceneNetworkSetup : MonoBehaviour
{
    private void Start()
    {
        SetupNetworkRunner();
        SetupGameManager();
        SetupPlayerSpawning();
    }

    private void SetupNetworkRunner()
    {
        var runner = FindObjectOfType<NetworkRunner>();
        if (runner == null)
        {
            var runnerGO = new GameObject("NetworkRunner");
            runner = runnerGO.AddComponent<NetworkRunner>();
        }
    }

    private void SetupGameManager()
    {
        var gameManager = FindObjectOfType<GameManager>();
        if (gameManager == null)
        {
            var gmGO = new GameObject("GameManager");
            gameManager = gmGO.AddComponent<GameManager>();
        }
    }
}
```

### 2. UI系统集成

#### Canvas配置
```csharp
// UI Canvas 标准配置
public class UICanvasSetup : MonoBehaviour
{
    [Header("Canvas Settings")]
    public RenderMode renderMode = RenderMode.ScreenSpaceOverlay;
    public int sortingOrder = 0;

    [Header("Canvas Scaler Settings")]
    public CanvasScaler.ScaleMode scaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
    public Vector2 referenceResolution = new Vector2(1920, 1080);
    public float matchWidthOrHeight = 0.5f;

    private void Start()
    {
        SetupCanvas();
        SetupCanvasScaler();
        SetupGraphicRaycaster();
    }

    private void SetupCanvas()
    {
        var canvas = GetComponent<Canvas>();
        canvas.renderMode = renderMode;
        canvas.sortingOrder = sortingOrder;
    }
}
```

#### 移动端UI适配
```csharp
// 移动端UI适配器
public class MobileUIAdapter : MonoBehaviour
{
    [Header("Mobile UI Settings")]
    public GameObject mobileUIRoot;
    public GameObject desktopUIRoot;
    public VirtualJoystick virtualJoystick;
    public TouchButton[] touchButtons;

    private void Start()
    {
        bool isMobile = Application.isMobilePlatform;

        if (mobileUIRoot != null)
            mobileUIRoot.SetActive(isMobile);

        if (desktopUIRoot != null)
            desktopUIRoot.SetActive(!isMobile);

        if (isMobile)
        {
            SetupMobileControls();
        }
    }

    private void SetupMobileControls()
    {
        // 配置虚拟摇杆
        if (virtualJoystick != null)
        {
            virtualJoystick.joystickType = VirtualJoystick.JoystickType.Dynamic;
            virtualJoystick.joystickRange = 50f;
        }

        // 配置触摸按钮
        foreach (var button in touchButtons)
        {
            button.enableHapticFeedback = true;
            button.animationDuration = 0.1f;
        }
    }
}
```

### 3. 任务系统集成

#### 任务站点配置
```csharp
// 任务站点组件
public class TaskStation : NetworkBehaviour
{
    [Header("Task Configuration")]
    public string taskId;
    public string taskName;
    public TaskType taskType;
    public float taskDuration = 5f;
    public bool isVisualTask = false;

    [Header("Interaction")]
    public float interactionRange = 2f;
    public LayerMask playerLayer = 1;

    [Networked] public bool IsCompleted { get; set; }
    [Networked] public PlayerRef AssignedPlayer { get; set; }

    public override void Spawned()
    {
        base.Spawned();

        // 初始化任务站点
        InitializeTaskStation();
    }

    private void InitializeTaskStation()
    {
        // 设置交互区域
        var trigger = gameObject.AddComponent<SphereCollider>();
        trigger.isTrigger = true;
        trigger.radius = interactionRange;

        // 注册到任务管理器
        var taskManager = FindObjectOfType<TaskManager>();
        taskManager?.RegisterTaskStation(this);
    }

    [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
    public void StartTask(PlayerRef player)
    {
        if (HasStateAuthority && !IsCompleted)
        {
            AssignedPlayer = player;
            // 开始任务逻辑
        }
    }
}
```

### 4. 性能优化配置

#### 对象池系统
```csharp
// 对象池管理器
public class ObjectPoolManager : MonoBehaviour
{
    [System.Serializable]
    public class PoolInfo
    {
        public string poolName;
        public GameObject prefab;
        public int poolSize = 10;
        public bool expandable = true;
    }

    [Header("Pool Configuration")]
    public PoolInfo[] pools;

    private Dictionary<string, Queue<GameObject>> poolDictionary;

    private void Start()
    {
        InitializePools();
    }

    private void InitializePools()
    {
        poolDictionary = new Dictionary<string, Queue<GameObject>>();

        foreach (var pool in pools)
        {
            var objectQueue = new Queue<GameObject>();

            for (int i = 0; i < pool.poolSize; i++)
            {
                var obj = Instantiate(pool.prefab);
                obj.SetActive(false);
                objectQueue.Enqueue(obj);
            }

            poolDictionary.Add(pool.poolName, objectQueue);
        }
    }

    public GameObject SpawnFromPool(string poolName, Vector3 position, Quaternion rotation)
    {
        if (!poolDictionary.ContainsKey(poolName))
        {
            Debug.LogWarning($"Pool {poolName} doesn't exist!");
            return null;
        }

        var objectToSpawn = poolDictionary[poolName].Dequeue();
        objectToSpawn.SetActive(true);
        objectToSpawn.transform.position = position;
        objectToSpawn.transform.rotation = rotation;

        poolDictionary[poolName].Enqueue(objectToSpawn);

        return objectToSpawn;
    }
}
```

### 5. 调试和开发工具

#### 场景调试器
```csharp
// 场景调试工具
public class SceneDebugger : MonoBehaviour
{
    [Header("Debug Settings")]
    public bool showDebugInfo = true;
    public bool enableCheatCommands = false;
    public KeyCode debugToggleKey = KeyCode.F1;

    private bool debugUIVisible = false;

    private void Update()
    {
        if (Input.GetKeyDown(debugToggleKey))
        {
            debugUIVisible = !debugUIVisible;
        }

        if (enableCheatCommands)
        {
            HandleCheatCommands();
        }
    }

    private void OnGUI()
    {
        if (!showDebugInfo || !debugUIVisible) return;

        GUILayout.BeginArea(new Rect(10, 10, 300, 400));
        GUILayout.Label("=== Scene Debug Info ===");

        // 显示网络状态
        var runner = FindObjectOfType<NetworkRunner>();
        if (runner != null)
        {
            GUILayout.Label($"Network State: {runner.State}");
            GUILayout.Label($"Is Server: {runner.IsServer}");
            GUILayout.Label($"Player Count: {runner.ActivePlayers.Count()}");
        }

        // 显示游戏状态
        var gameManager = GameManager.Instance;
        if (gameManager != null)
        {
            GUILayout.Label($"Game Active: {gameManager.IsGameActive}");
            GUILayout.Label($"Current Round: {gameManager.CurrentRound}");
        }

        GUILayout.EndArea();
    }

    private void HandleCheatCommands()
    {
        if (Input.GetKeyDown(KeyCode.F2))
        {
            // 快速开始游戏
            GameManager.Instance?.StartGame();
        }

        if (Input.GetKeyDown(KeyCode.F3))
        {
            // 召开紧急会议
            var localPlayer = NetworkRunner.LocalPlayer;
            GameManager.Instance?.CallMeeting(localPlayer, false);
        }
    }
}
```

## 📊 场景性能监控

### 性能指标监控
```csharp
// 性能监控器
public class ScenePerformanceMonitor : MonoBehaviour
{
    [Header("Performance Settings")]
    public bool enableMonitoring = true;
    public float updateInterval = 1f;

    private float frameCount = 0;
    private float dt = 0f;
    private float fps = 0f;
    private float ms = 0f;

    private void Update()
    {
        if (!enableMonitoring) return;

        frameCount++;
        dt += Time.deltaTime;

        if (dt > updateInterval)
        {
            fps = frameCount / dt;
            ms = dt / frameCount * 1000f;
            frameCount = 0;
            dt -= updateInterval;

            // 记录性能数据
            LogPerformanceData();
        }
    }

    private void LogPerformanceData()
    {
        var memoryUsage = System.GC.GetTotalMemory(false) / 1024 / 1024; // MB

        Debug.Log($"Performance: FPS={fps:F1}, MS={ms:F1}, Memory={memoryUsage}MB");

        // 如果性能过低，触发优化
        if (fps < 30f)
        {
            TriggerPerformanceOptimization();
        }
    }

    private void TriggerPerformanceOptimization()
    {
        var optimizer = FindObjectOfType<MobilePerformanceOptimizer>();
        optimizer?.OptimizeForLowPerformance();
    }
}
```

---

## 📚 相关文档

- [项目结构与代码绑定使用文档](./项目结构与代码绑定使用文档.md)
- [快速参考指南](./快速参考指南.md)
- [README](./README.md)

## 🔧 工具和资源

### Unity编辑器工具
- **Scene Hierarchy Organizer**: 自动整理场景层次结构
- **Network Debug Visualizer**: 可视化网络状态和RPC调用
- **Performance Profiler Integration**: 集成Unity Profiler数据

### 预制体模板
- **GameScene Template**: 标准游戏场景模板
- **UI Canvas Template**: 标准UI画布模板
- **Network Manager Template**: 网络管理器模板

### 配置文件
- **Scene Settings**: 场景特定的配置文件
- **Performance Profiles**: 不同平台的性能配置
- **Network Settings**: 网络相关配置

---

**文档版本**: v1.0
**最后更新**: 2025-08-22
**适用版本**: Unity 6000.1.1f1+ (Unity 6) / Goose Duck Kill 3
