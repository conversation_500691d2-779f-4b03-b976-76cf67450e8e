# Goose Duck Kill 3 项目结构与代码绑定使用文档

## 📋 项目概述

**Goose Duck Kill 3** 是一个基于Unity的多人在线游戏项目，采用自定义网络框架替代Photon Fusion，实现了完整的多人游戏功能。项目采用模块化架构设计，支持跨平台部署和移动端优化。

### 🎯 核心特性
- **自定义网络框架**: 完全摆脱Photon Fusion依赖，降低成本
- **模块化架构**: 清晰的代码分层和组件解耦
- **跨平台支持**: PC、移动端统一代码库
- **性能优化**: 针对移动设备的专门优化
- **完整测试体系**: EditMode和PlayMode测试覆盖

## 🏗️ 项目架构

### 核心模块依赖关系图

```
CustomNetworking (基础网络框架)
    ↑
    ├── GooseDuckKill.Core (游戏核心逻辑)
    ├── GooseDuckKill.Network (网络管理层)
    ├── GooseDuckKill.Player (玩家系统)
    ├── GooseDuckKill.UI (用户界面)
    ├── GooseDuckKill.Mobile (移动端适配)
    └── GooseDuckKill.Examples (示例代码)
```

## 📁 目录结构详解

### Assets/Scripts/ 核心代码结构

```
Assets/Scripts/
├── Core/                           # 🎮 游戏核心系统
│   ├── GameManager.cs              # 游戏生命周期管理器
│   ├── GameStateManager.cs         # 游戏状态机
│   ├── RoleManager.cs              # 角色分配与管理
│   ├── ScoreManager.cs             # 计分与胜利条件
│   └── GooseDuckKill.Core.asmdef   # 程序集定义
│
├── CustomNetworking/               # 🌐 自定义网络框架
│   ├── Core/                       # 核心网络组件
│   │   ├── NetworkRunner.cs        # 网络运行器
│   │   ├── NetworkBehaviour.cs     # 网络行为基类
│   │   ├── NetworkObject.cs        # 网络对象管理
│   │   └── RPC/                    # RPC系统
│   ├── Components/                 # 网络组件
│   ├── WebSocket/                  # WebSocket传输层
│   ├── Synchronization/            # 状态同步系统
│   ├── Authority/                  # 权限管理
│   ├── Security/                   # 安全验证
│   └── Optimization/               # 性能优化
│
├── Network/                        # 🔗 网络管理层
│   ├── NetworkManager.cs           # 网络连接管理
│   ├── ImprovedNetworkManager.cs   # 增强网络管理器
│   ├── WebSocketEventSystem.cs     # WebSocket事件系统
│   ├── AuthManager.cs              # 认证管理
│   └── Models/                     # 网络数据模型
│
├── Player/                         # 👤 玩家系统
│   ├── PlayerController.cs         # 玩家控制器
│   ├── PlayerMovement.cs           # 移动系统
│   ├── PlayerInteraction.cs        # 交互系统
│   ├── PlayerNetwork.cs            # 玩家网络同步
│   └── Mobile/                     # 移动端输入适配
│
├── UI/                            # 🖥️ 用户界面
│   ├── ConnectionUI.cs            # 连接界面
│   └── Mobile/                    # 移动端UI组件
│       ├── VirtualJoystick.cs     # 虚拟摇杆
│       └── TouchButton.cs         # 触摸按钮
│
├── Mobile/                        # 📱 移动端优化
│   └── MobilePerformanceOptimizer.cs # 性能优化器
│
├── Tasks/                         # 📋 任务系统
├── Utils/                         # 🔧 工具类
└── Editor/                        # ⚙️ 编辑器工具
    ├── BurstCompilationFix.cs     # Burst编译修复
    └── URPConfigurationValidator.cs # URP配置验证
```

### Assets/Tests/ 测试结构

```
Assets/Tests/
├── EditMode/                      # 编辑器模式测试
│   ├── Unit/                      # 单元测试
│   ├── Integration/               # 集成测试
│   └── Utilities/                 # 测试工具
├── PlayMode/                      # 运行时测试
│   ├── Functional/                # 功能测试
│   ├── Performance/               # 性能测试
│   ├── EndToEnd/                  # 端到端测试
│   └── Utilities/                 # 测试工具
└── Shared/                        # 共享测试资源
    ├── Fixtures/                  # 测试夹具
    ├── Mocks/                     # 模拟对象
    ├── Helpers/                   # 测试辅助
    └── Data/                      # 测试数据
```

## 🔧 程序集定义 (Assembly Definitions)

### 依赖关系图

```mermaid
graph TD
    A[CustomNetworking] --> B[GooseDuckKill.Core]
    A --> C[GooseDuckKill.Network]
    A --> D[GooseDuckKill.Player]
    A --> E[GooseDuckKill.UI]
    A --> F[GooseDuckKill.Mobile]
    A --> G[GooseDuckKill.Examples]

    C --> B
    D --> B
    E --> B
    E --> C

    H[GooseDuckKill.Tests.Shared] --> A
    H --> B
    H --> C
    H --> D
    H --> E

    I[GooseDuckKill.Tests.EditMode] --> H
    J[GooseDuckKill.Tests.PlayMode] --> H
```

### 核心程序集配置

| 程序集名称 | 根命名空间 | 主要依赖 | 用途 |
|-----------|-----------|----------|------|
| `CustomNetworking` | `CustomNetworking` | 无 | 基础网络框架 |
| `GooseDuckKill.Core` | `GooseDuckKill.Core` | CustomNetworking, Network | 游戏核心逻辑 |
| `GooseDuckKill.Network` | `GooseDuckKill.Network` | CustomNetworking | 网络管理层 |
| `GooseDuckKill.Player` | `GooseDuckKill.Player` | CustomNetworking, Core, InputSystem | 玩家系统 |
| `GooseDuckKill.UI` | `GooseDuckKill.UI` | CustomNetworking, Core, Network, TextMeshPro | 用户界面 |
| `GooseDuckKill.Mobile` | `GooseDuckKill.Mobile` | CustomNetworking | 移动端适配 |

## 🎮 核心组件详解

### 1. GameManager - 游戏管理器

**位置**: `Assets/Scripts/Core/GameManager.cs`
**继承**: `NetworkBehaviour`
**作用**: 中央游戏协调器，管理游戏生命周期和全局状态

#### 核心功能
- 游戏状态管理 (大厅→开始→游戏→会议→结束)
- 网络同步的游戏计时器
- 会议系统管理
- 组件协调 (StateManager, RoleManager, ScoreManager)

#### 关键属性
```csharp
[Networked] private NetworkBool IsGameActive { get; set; }
[Networked] private int CurrentRound { get; set; }
[Networked] private TickTimer GameTimer { get; set; }
[Networked] private NetworkBool IsInMeeting { get; set; }
```

#### 使用示例
```csharp
// 获取GameManager实例
var gameManager = GameManager.Instance;

// 开始游戏
gameManager.StartGame();

// 监听游戏事件
gameManager.OnGameStarted += () => Debug.Log("游戏开始!");
gameManager.OnMeetingCalled += (caller, isBodyReport) => HandleMeeting(caller, isBodyReport);
```

### 2. NetworkRunner - 网络运行器

**位置**: `Assets/Scripts/CustomNetworking/Core/NetworkRunner.cs`
**作用**: 自定义网络框架的核心，替代Photon Fusion的NetworkRunner

#### 核心功能
- 多种游戏模式支持 (Server, Host, Client, AutoHostOrClient)
- Tick-based确定性模拟
- 网络对象生命周期管理
- RPC系统集成

#### 游戏模式
```csharp
public enum GameMode
{
    Server,           // 专用服务器
    Host,            // 主机模式
    Client,          // 客户端
    AutoHostOrClient // 自动选择
}
```

#### 使用示例
```csharp
// 启动网络运行器
var startArgs = new StartGameArgs
{
    GameMode = NetworkRunner.GameMode.Host,
    SessionName = "MyRoom",
    MaxPlayers = 10
};

var result = await runner.StartGame(startArgs);
if (result.Ok)
{
    Debug.Log("网络启动成功");
}
```

### 3. PlayerController - 玩家控制器

**位置**: `Assets/Scripts/Player/PlayerController.cs`
**继承**: `NetworkBehaviour`
**作用**: 管理单个玩家的行为、状态和网络同步

#### 核心功能
- 玩家状态管理 (Normal, Dead, Ghost, InTask, InMeeting)
- 网络输入处理
- 移动和交互系统集成
- 角色能力管理

#### 玩家状态
```csharp
public enum PlayerState
{
    Normal,    // 正常状态
    Dead,      // 死亡状态
    Ghost,     // 幽灵状态
    InTask,    // 执行任务中
    InMeeting  // 会议中
}
```

#### 使用示例
```csharp
// 改变玩家状态
playerController.ChangeState(PlayerState.Dead);

// 监听状态变化
playerController.OnPlayerStateChanged += (newState) =>
{
    Debug.Log($"玩家状态变为: {newState}");
};
```

## 🔗 代码绑定使用指南

### 1. 网络对象创建与绑定

#### 步骤1: 创建NetworkObject
```csharp
// 在服务器端生成网络对象
var playerPrefab = Resources.Load<GameObject>("PlayerPrefab");
var networkObject = Runner.Spawn(playerPrefab, spawnPosition, spawnRotation, inputAuthority);
```

#### 步骤2: 获取NetworkBehaviour组件
```csharp
// 获取玩家控制器
var playerController = networkObject.GetComponent<PlayerController>();
if (playerController != null)
{
    // 设置玩家属性
    playerController.SetPlayerName("玩家名称");
    playerController.SetPlayerColor(Color.red);
}
```

### 2. RPC调用模式

#### 基础RPC调用
```csharp
// 在NetworkBehaviour中调用RPC
[Rpc(RpcSources.All, RpcTargets.All)]
public void ShowMessage(string message)
{
    Debug.Log($"收到消息: {message}");
}

// 调用RPC
ShowMessage("Hello World!");
```

#### 高级RPC调用
```csharp
// 带参数的RPC调用
RPC("MethodName", RpcTargets.Others, reliable: true, RpcPriority.High,
    PlayerRef.None, param1, param2, param3);
```

### 3. 事件系统绑定

#### 网络事件监听
```csharp
private void OnEnable()
{
    // 监听网络事件
    NetworkEvents.OnConnected += HandleConnected;
    NetworkEvents.OnDisconnected += HandleDisconnected;
    NetworkEvents.OnPlayerJoined += HandlePlayerJoined;
}

private void OnDisable()
{
    // 取消监听
    NetworkEvents.OnConnected -= HandleConnected;
    NetworkEvents.OnDisconnected -= HandleDisconnected;
    NetworkEvents.OnPlayerJoined -= HandlePlayerJoined;
}
```

#### 游戏事件监听
```csharp
private void Start()
{
    var gameManager = GameManager.Instance;
    if (gameManager != null)
    {
        gameManager.OnGameStarted += HandleGameStarted;
        gameManager.OnGameEnded += HandleGameEnded;
        gameManager.OnMeetingCalled += HandleMeetingCalled;
    }
}
```

### 4. 移动端适配绑定

#### 虚拟摇杆绑定
```csharp
public class MobilePlayerController : MonoBehaviour
{
    [SerializeField] private VirtualJoystick movementJoystick;
    [SerializeField] private PlayerMovement playerMovement;

    private void Update()
    {
        if (movementJoystick != null && playerMovement != null)
        {
            Vector2 input = movementJoystick.InputVector;
            playerMovement.ProcessMovement(input);
        }
    }
}
```

#### 触摸按钮绑定
```csharp
public class MobileUI : MonoBehaviour
{
    [SerializeField] private TouchButton interactButton;
    [SerializeField] private TouchButton actionButton;

    private void Start()
    {
        // 绑定按钮事件
        interactButton.OnButtonPressed += HandleInteract;
        actionButton.OnButtonPressed += HandleAction;
    }

    private void HandleInteract()
    {
        // 处理交互逻辑
        var playerController = FindObjectOfType<PlayerController>();
        playerController?.TriggerInteraction();
    }
}
```

## 🚀 快速开始指南

### 1. 创建新的网络对象

1. 创建GameObject并添加NetworkObject组件
2. 添加自定义NetworkBehaviour脚本
3. 在NetworkBehaviour中实现网络逻辑
4. 制作成Prefab并放入Resources文件夹

### 2. 实现自定义NetworkBehaviour

```csharp
using CustomNetworking.Core;

public class MyNetworkBehaviour : NetworkBehaviour
{
    [Networked] public int MyNetworkedProperty { get; set; }

    public override void Spawned()
    {
        base.Spawned();
        // 对象生成时的初始化逻辑
    }

    public override void FixedUpdateNetwork()
    {
        // 服务器端网络更新逻辑
        if (HasStateAuthority)
        {
            // 只在有状态权限时执行
        }
    }

    public override void Render()
    {
        // 客户端渲染更新逻辑
    }
}
```

### 3. 集成UI系统

```csharp
public class GameUI : MonoBehaviour
{
    [SerializeField] private ConnectionUI connectionUI;

    private void Start()
    {
        // 监听网络状态变化
        NetworkEvents.OnConnected += () => connectionUI.ShowLobby();
        NetworkEvents.OnDisconnected += (reason) => connectionUI.ShowConnectionPanel();
    }
}
```

## 📱 移动端优化配置

### 性能配置文件
```csharp
// 在MobilePerformanceOptimizer中配置
var profile = new PerformanceProfile
{
    profileName = "移动端优化",
    targetFrameRate = 30,
    qualityLevel = 1,
    renderScale = 0.8f,
    enableVSync = false
};

MobilePerformanceOptimizer.Instance.ApplyProfile(profile);
```

### 网络优化设置
```csharp
// 移动端网络优化
var optimizer = NetworkBandwidthOptimizer.Instance;
optimizer.SetMaxBandwidth(1024 * 1024); // 1MB/s
optimizer.EnableMobileOptimization(true);
```

## 🧪 测试系统使用

### EditMode测试示例
```csharp
[Test]
public void GameManager_StartGame_SetsCorrectState()
{
    // Arrange
    var gameManager = CreateTestGameManager();

    // Act
    gameManager.StartGame();

    // Assert
    Assert.IsTrue(gameManager.IsGameActive);
    Assert.AreEqual(1, gameManager.CurrentRound);
}
```

### PlayMode测试示例
```csharp
[UnityTest]
public IEnumerator NetworkRunner_StartGame_ConnectsSuccessfully()
{
    // Arrange
    var runner = CreateTestNetworkRunner();
    var args = new StartGameArgs { GameMode = GameMode.Host };

    // Act
    var task = runner.StartGame(args);
    yield return new WaitUntil(() => task.IsCompleted);

    // Assert
    Assert.IsTrue(task.Result.Ok);
    Assert.IsTrue(runner.IsRunning);
}
```

## 📚 最佳实践

### 1. 网络同步最佳实践
- 使用`[Networked]`属性标记需要同步的字段
- 在`HasStateAuthority`检查后修改网络属性
- 合理使用RPC的可靠性和优先级设置

### 2. 性能优化建议
- 移动端启用性能优化配置
- 使用对象池管理频繁创建/销毁的对象
- 合理设置网络同步频率

### 3. 代码组织建议
- 遵循程序集依赖关系，避免循环依赖
- 使用事件系统解耦组件间通信
- 将平台特定代码放在对应的程序集中

## 🔧 配置文件说明

### Unity包依赖 (Packages/manifest.json)
```json
{
  "dependencies": {
    "com.unity.inputsystem": "1.14.0",        // 新输入系统
    "com.unity.render-pipelines.universal": "17.1.0", // URP渲染管线
    "com.unity.test-framework": "2.0.1",      // 测试框架
    "com.unity.nuget.newtonsoft-json": "3.2.1", // JSON序列化
    "com.unity.multiplayer.center": "1.0.0"   // 多人游戏中心
  }
}
```

### 项目设置 (Directory.Build.props)
```xml
<Project>
  <PropertyGroup>
    <!-- 禁用命名空间与文件夹结构匹配的规则 -->
    <NoWarn>$(NoWarn);IDE0130</NoWarn>
    <!-- 启用EditorConfig支持 -->
    <EnableEditorConfigSupport>true</EnableEditorConfigSupport>
  </PropertyGroup>
</Project>
```

### OmniSharp配置 (omnisharp.json)
```json
{
  "FormattingOptions": {
    "EnableEditorConfigSupport": true,
    "OrganizeImports": true
  },
  "RoslynExtensionsOptions": {
    "EnableAnalyzersSupport": true,
    "EnableImportCompletion": true
  }
}
```

## 🛠️ 开发工具和编辑器扩展

### Burst编译修复工具
**位置**: `Assets/Scripts/Editor/BurstCompilationFix.cs`

```csharp
// 使用菜单项修复Burst编译问题
[MenuItem("Tools/Fix Burst Compilation Issues")]
public static void FixBurstCompilationIssues()
{
    // 清理编译缓存并强制重新编译
}
```

### URP配置验证器
**位置**: `Assets/Scripts/Editor/URPConfigurationValidator.cs`

用于验证Universal Render Pipeline配置的正确性，确保渲染设置与Unity版本兼容。

## 🌐 网络架构深入解析

### 传输层架构
```
应用层 (GameManager, PlayerController)
    ↓
RPC层 (RpcManager, NetworkBehaviour)
    ↓
同步层 (NetworkSynchronizationManager)
    ↓
传输层 (WebSocketTransport, UnifiedNetworkConnection)
    ↓
物理层 (TCP/WebSocket)
```

### 网络消息流程
1. **客户端输入** → PlayerInput收集
2. **输入打包** → NetworkInput结构
3. **网络传输** → WebSocket/TCP发送
4. **服务器处理** → NetworkRunner.FixedUpdateNetwork
5. **状态同步** → NetworkedProperty自动同步
6. **客户端渲染** → NetworkBehaviour.Render

### 权限管理系统
```csharp
// 权限类型
public enum AuthorityType
{
    StateAuthority,    // 状态权限 - 可修改对象状态
    InputAuthority,    // 输入权限 - 可发送输入
    ProxyAuthority     // 代理权限 - 可代理操作
}

// 权限检查
if (HasStateAuthority)
{
    // 只有状态权限持有者可以修改网络属性
    NetworkedProperty = newValue;
}
```

## 📊 性能监控和优化

### 网络性能监控
```csharp
// 获取网络统计信息
var stats = NetworkRunner.GetNetworkStats();
Debug.Log($"RTT: {stats.RoundTripTime}ms");
Debug.Log($"Packet Loss: {stats.PacketLoss}%");
Debug.Log($"Bandwidth: {stats.BandwidthUsage} bytes/s");
```

### 移动端性能配置
```csharp
// 自动性能适配
public class AutoPerformanceAdapter : MonoBehaviour
{
    private void Start()
    {
        var optimizer = MobilePerformanceOptimizer.Instance;

        // 根据设备性能自动选择配置
        if (SystemInfo.systemMemorySize < 3000) // 3GB以下
        {
            optimizer.ApplyProfile(PerformanceProfile.LowEnd);
        }
        else if (SystemInfo.systemMemorySize < 6000) // 6GB以下
        {
            optimizer.ApplyProfile(PerformanceProfile.MidRange);
        }
        else
        {
            optimizer.ApplyProfile(PerformanceProfile.HighEnd);
        }
    }
}
```

## 🔍 调试和故障排除

### 网络调试工具
```csharp
// 启用网络调试
NetworkDebugManager.Instance.EnableDebugLogging(true);
NetworkDebugManager.Instance.ShowNetworkStats(true);

// 调试RPC调用
[Rpc(RpcSources.All, RpcTargets.All)]
public void DebugRpc(string message)
{
    Debug.Log($"[RPC Debug] {message} from {Runner.LocalPlayer}");
}
```

### 常见问题解决

#### 1. 网络连接失败
```csharp
// 检查网络状态
if (NetworkRunner.State == NetworkRunner.States.Failed)
{
    Debug.LogError("网络连接失败，尝试重新连接");
    await NetworkRunner.Shutdown();
    await NetworkRunner.StartGame(lastStartArgs);
}
```

#### 2. 同步问题
```csharp
// 强制同步检查
public override void Render()
{
    // 检测属性变化
    if (_changeDetector.DetectChange(nameof(MyProperty), MyProperty))
    {
        OnPropertyChanged?.Invoke(MyProperty);
    }
}
```

#### 3. 移动端性能问题
```csharp
// 动态质量调整
private void Update()
{
    if (Application.targetFrameRate > 0)
    {
        float currentFPS = 1.0f / Time.deltaTime;
        if (currentFPS < Application.targetFrameRate * 0.8f)
        {
            // 降低质量设置
            QualitySettings.DecreaseLevel();
        }
    }
}
```

## 📋 部署和构建指南

### 构建配置
```csharp
// 构建前预处理
[InitializeOnLoadMethod]
static void OnProjectLoadedInEditor()
{
    // 验证构建设置
    ValidateBuildSettings();

    // 清理临时文件
    CleanupTempFiles();

    // 检查程序集引用
    ValidateAssemblyReferences();
}
```

### 平台特定设置

#### PC平台
- 目标框架: .NET Framework 4.8
- 渲染API: DirectX 11/12
- 网络传输: TCP + WebSocket

#### 移动平台
- 目标框架: .NET Standard 2.1
- 渲染API: Vulkan/Metal
- 网络传输: WebSocket (优化)
- 性能配置: 自动适配

### 发布检查清单
- [ ] 所有测试通过
- [ ] 网络连接稳定
- [ ] 移动端性能优化
- [ ] 资源打包完整
- [ ] 版本号更新
- [ ] 构建配置正确

## 🤝 贡献指南

### 代码规范
1. **命名约定**: 使用PascalCase命名公共成员，camelCase命名私有成员
2. **注释规范**: 使用XML文档注释标记公共API
3. **程序集组织**: 新功能放入对应的程序集中
4. **测试覆盖**: 新功能必须包含对应的单元测试

### 提交规范
```
feat: 添加新功能
fix: 修复bug
docs: 更新文档
style: 代码格式调整
refactor: 代码重构
test: 添加测试
chore: 构建过程或辅助工具的变动
```

### 开发流程
1. 创建功能分支
2. 实现功能和测试
3. 运行完整测试套件
4. 提交代码审查
5. 合并到主分支

---

## 📚 参考资源

### 官方文档
- [Unity Netcode Documentation](https://docs.unity3d.com/Packages/com.unity.netcode.gameobjects@latest)
- [Unity Input System](https://docs.unity3d.com/Packages/com.unity.inputsystem@latest)
- [Universal Render Pipeline](https://docs.unity3d.com/Packages/com.unity.render-pipelines.universal@latest)

### 社区资源
- [Unity Multiplayer Networking](https://unity.com/products/netcode)
- [Mobile Game Performance](https://unity.com/how-to/mobile-game-performance)

### 项目相关文档
- `README.md` - 项目概述和快速开始
- `MIGRATION_DOCUMENTATION.md` - 迁移文档
- `Assets/Tests/README.md` - 测试系统说明

---

*本文档基于项目当前状态编写，随着项目发展可能需要更新。如有疑问，请参考代码注释或联系开发团队。*

**文档版本**: v1.0
**最后更新**: 2025-08-22
**适用版本**: Unity 6000.1.1f1+ (Unity 6) / .NET Framework 4.8+
